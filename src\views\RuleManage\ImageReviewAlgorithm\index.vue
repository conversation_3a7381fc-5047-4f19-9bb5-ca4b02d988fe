<!-- eslint-disable vue/v-on-event-hyphenation -->
<template>
  <!-- 算法管理 -->
  <UmvContent>
    <UmvQuery
      v-model="searchData"
      :opts="queryOpts"
      label-width="80px"
      @check="handleSearch"
      @reset="handleReset"
    />

    <UmvTable
      v-loading="loading"
      :data="tableData"
      :default-stripe="false"
      @refresh="queryAlgorithmList"
    >
      <template #tools>
        <el-button type="primary" size="small" v-track:click.btn @click="openDialog('add', null)">{{
          t('common.add')
        }}</el-button>
      </template>
      <el-table-column label="序号" width="60px">
        <template #default="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="algorithmName" label="算法名称" width="100" />
      <el-table-column
        prop="algorithmType"
        label="算法类型"
        min-width="100"
        :formatter="convertType"
      />
      <el-table-column prop="apiUrl" label="接口地址" min-width="120" />
      <el-table-column prop="algorithmDesc" label="描述" min-width="200" />
      <el-table-column prop="algorithmId" label="算法ID" min-width="200" />
      <el-table-column prop="versionNo" label="版本号" min-width="200" />
      <el-table-column prop="createTime" label="创建时间" min-width="200" />
      <el-table-column fixed="right" label="操作" width="110">
        <template #default="scope">
          <el-button type="primary" link v-track:click.btn @click="openDialog('edit', scope.row)">{{
            t('common.edit')
          }}</el-button>
          <el-button
            type="primary"
            link
            class="color-danger"
            v-track:click.btn
            @click="openDialog('delete', scope.row)"
            >{{ t('common.delete') }}</el-button
          >
        </template>
      </el-table-column>

      <template #pagination>
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="query.page"
          :page-sizes="[10, 30, 50]"
          :page-size="query.pageSize"
          :total="query.total"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </template>
    </UmvTable>
    <!-- 编辑弹窗 -->
    <AlgorithmAddDlg
      v-if="handleRule"
      :handleRule="handleRule"
      :edit="edit"
      :algorithmTypeList="algorithmTypeList"
      :aiNoList="aiNoList"
      :auditUrl="auditUrl"
      :aiHandleList="aiHandleList"
      :algorithmInfo="algorithmInfo"
      @closeDialog="closeDialog"
    />
    <!-- 删除弹窗 -->
    <AlgorithmDeleteDlg
      v-if="deleteRule"
      :deleteRule="deleteRule"
      :dataInfo="algorithmInfo"
      @closeDialog="closeDialog"
    />
  </UmvContent>
</template>

<script setup lang="tsx">
defineOptions({
  name: 'ImageReviewAlgorithm'
})

const { t } = useI18n()
import UmvContent from '@/components/UmvContent'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import UmvTable from '@/components/UmvTable'
import AlgorithmAddDlg from './components/AlgorithmAddDlg.vue'
import AlgorithmDeleteDlg from './components/AlgorithmDeleteDlg.vue'
import { ref, reactive, onMounted } from 'vue'
import { getAlgorithmList, algorithmNoList } from '@/api/RuleManage/ImageReviewAlgorithm'
import { ElInput } from 'element-plus'

const loading = ref(false)

const handleRule = ref(false) // 新增审核项弹窗

const deleteRule = ref(false) // 删除弹窗

const edit = ref(false) // 是否为编辑

//算法编号Map
const aiNoMap = new Map()

//算法类型
const algorithmTypeList = [
  {
    typeKey: 'audit',
    typeValue: '图像审核'
  },
  {
    typeKey: 'handle',
    typeValue: '图像处理'
  }
]

let aiNoList: any[] = []
let aiHandleList: any[] = []
let auditUrl = ''
const tableData = ref([
  {
    algorithmName: '',
    algorithmType: '',
    apiUrl: '',
    algorithmDesc: '',
    algorithmId: '',
    versionNo: '',
    createTime: '',
    right: '操作'
  }
])

//分页条件
const query = reactive({
  total: 0,
  page: 1,
  pageSize: 10
})

//查询条件
const searchData = reactive({
  algorithmType: '', // 算法类型
  algorithmName: '', // 算法名称
  algorithmId: '' // 算法ID
})

// UmvQuery 配置选项
const queryOpts = ref<Record<string, QueryOption>>({
  algorithmName: {
    label: '算法名称',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElInput
        v-model={form.algorithmName}
        clearable
        placeholder="请输入算法名称"
        maxlength="40"
        style="width: 100%"
      />
    )
  },
  algorithmId: {
    label: '算法ID',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElInput
        v-model={form.algorithmId}
        clearable
        placeholder="请输入算法ID"
        maxlength="20"
        style="width: 100%"
      />
    )
  }
})

const algorithmInfo = ref<any>({}) //算法信息

//转换算法类型
const convertType = (row) => {
  if (row.algorithmType == 'audit') {
    return '图像审核'
  } else if (row.algorithmType == 'handle') {
    return '图像处理'
  } else {
    return ''
  }
}
// 删除未使用的 convertNo 函数

// 获取算法列表
const queryAlgorithmList = async () => {
  const data = {
    // algorithmType: searchData.algorithmType, // 算法类型
    algorithmType: 'audit', // 算法类型
    algorithmName: searchData.algorithmName, // 算法名称
    algorithmId: searchData.algorithmId, // 算法ID
    pageNum: query.page,
    pageSize: query.pageSize
  }
  loading.value = true
  try {
    const resData = await getAlgorithmList(data)
    tableData.value = resData?.list || []
    query.total = resData?.total || 0
  } finally {
    loading.value = false
  }
}
// 获取AI规则列表
const queryAlgorithmNoList = async () => {
  const data = {}
  loading.value = true
  try {
    const resData = await algorithmNoList(data)
    aiNoList = resData?.auditList || []
    aiHandleList = resData?.handleList || []
    auditUrl = resData?.auditUrl || ''
  } finally {
    loading.value = false
  }
}
onMounted(() => {
  if (aiNoMap.size == 0) {
    aiNoList.forEach((item) => {
      aiNoMap.set(item.aiType, item.aiValue)
    })
  }
  queryAlgorithmList()
  queryAlgorithmNoList()
})

// 打开算法添加/编辑弹窗
const openDialog = (type, obj) => {
  switch (type) {
    // 新增
    case 'add':
      handleRule.value = true
      edit.value = false
      break
    // 编辑
    case 'edit':
      edit.value = true
      algorithmInfo.value = obj
      handleRule.value = true
      break
    // 删除
    case 'delete':
      algorithmInfo.value = obj
      deleteRule.value = true
      break
  }
}
// 关闭添加弹窗
const closeDialog = (result) => {
  edit.value = false
  handleRule.value = false
  deleteRule.value = false
  if (result) {
    queryAlgorithmList()
  }
}

// 删除未使用的 handleClose 函数

// 搜索
const handleSearch = () => {
  queryAlgorithmList()
}
// 重置
const handleReset = () => {
  searchData.algorithmType = ''
  searchData.algorithmName = ''
  searchData.algorithmId = ''
  queryAlgorithmList()
}

// 改变页数
const handlePageChange = (val: number) => {
  query.page = val
  queryAlgorithmList()
}
// 改变条数
const handleSizeChange = (val: number) => {
  query.page = 1
  query.pageSize = val
  queryAlgorithmList()
}
</script>

<style scope lang="less"></style>
