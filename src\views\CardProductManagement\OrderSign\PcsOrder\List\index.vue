<!-- PCS订单签审-个人化 -->
<template>
  <UmvContent>
    <UmvQuery
      v-model="queryParams"
      :opts="queryOpts"
      label-width="100px"
      @check="handleQuery"
      @reset="resetQuery"
      :col-length-map="{
        2400: 6, // 屏幕宽度 >= 2560px 时使用 8 列
        1900: 5, // 屏幕宽度 >= 1920px 时使用 8 列
        1600: 4, // 屏幕宽度 >= 1600px 且 < 1920px 时使用 6 列
        1280: 4, // 屏幕宽度 >= 1280px 且 < 1600px 时使用 5 列
        1100: 3, // 屏幕宽度 >= 1280px 且 < 1600px 时使用 5 列
        1000: 2, // 屏幕宽度 >= 1000px 且 < 1280px 时使用 3 列
        768: 2, // 屏幕宽度 >= 768px 且 < 1000px 时使用 2 列
        0: 1 // 屏幕宽度 < 768px 时使用 1 列
      }"
    />

    <UmvTable :data="list" :columns="columns" v-loading="loading" @refresh="getList">
      <template #tools>
        <!-- 这里可以添加其他工具按钮 -->
      </template>
    </UmvTable>

    <template #pagination>
      <Pagination
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        :total="total"
      />
    </template>
  </UmvContent>
</template>

<script setup lang="tsx">
import { ref, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { ElSelect, ElOption, ElTag, ElLink } from 'element-plus'
import UmvContent from '@/components/UmvContent'
import { UmvQuery, type QueryOption, type QueryForm } from '@/components/UmvQuery'
import UmvTable from '@/components/UmvTable'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { SearchWildcardInput } from '@/components/SearchWildcardInput'
import { CustomerSelect } from '@/components/CustomerSelect'
import Pagination from '@/components/Pagination/index.vue'
import * as pcsOrderApi from '@/api/OrderHandle/pcsOrder/index'
import { useDataMap } from '@/views/CardProductManagement/OrderSign/PlmOrder/hooks/useDataMap'

// 组件定义
defineOptions({
  name: 'PcsOrderList'
})

// 国际化和路由
const { t } = useI18n()
const router = useRouter()
const message = useMessage()

// 数据映射
const { orderTypeMapper } = useDataMap()

// 查询参数
const queryParams = ref({
  orderCode: '',
  customerName: '',
  cardCode: '',
  pcsStatus: '',
  taskCode: '',
  pageNo: 1,
  pageSize: 10
})

// 列表数据
const loading = ref(false)
const list = ref([])
const total = ref(0)

// PCS状态选项
const pcsStatusOptions = ref([
  {
    label: '待评审',
    value: 0
  },
  {
    label: '完成',
    value: 1
  }
])

// UmvQuery 配置
const queryOpts = ref<Record<string, QueryOption>>({
  orderCode: {
    label: '订单编号',
    defaultVal: '',
    controlRender: (form: QueryForm) => {
      return (
        <SearchWildcardInput
          v-model={form.orderCode}
          placeholder="请输入"
          clearable
          maxlength={50}
          show-word-limit
        />
      )
    }
  },
  customerName: {
    label: '客户名称',
    defaultVal: '',
    controlRender: (form: QueryForm) => {
      return <CustomerSelect v-model:customer-name={form.customerName} isSearchByName clearable />
    }
  },
  cardCode: {
    label: '卡款编号',
    defaultVal: '',
    controlRender: (form: QueryForm) => {
      return (
        <SearchWildcardInput
          v-model={form.cardCode}
          placeholder="请输入"
          clearable
          maxlength={50}
          show-word-limit
        />
      )
    }
  },
  pcsStatus: {
    label: '状态',
    defaultVal: '',
    controlRender: (form: QueryForm) => {
      return (
        <ElSelect v-model={form.pcsStatus} placeholder="请选择" filterable clearable>
          {pcsStatusOptions.value.map((dict) => (
            <ElOption key={dict.value} label={dict.label} value={dict.value} />
          ))}
        </ElSelect>
      )
    }
  },
  taskCode: {
    label: '任务编号',
    defaultVal: '',
    controlRender: (form: QueryForm) => {
      return (
        <SearchWildcardInput
          v-model={form.taskCode}
          placeholder="请输入"
          clearable
          maxlength={50}
          show-word-limit
        />
      )
    }
  }
})

// UmvTable 列配置
const columns = ref<TableColumn[]>([
  {
    prop: 'taskCode',
    label: '任务编号',
    align: 'left',
    width: '170px',
    renderTemplate: (scope) => {
      return (
        <ElLink type="primary" underline={false} onClick={() => handleOpen(scope.row)}>
          {scope.row.taskCode}
        </ElLink>
      )
    }
  },
  {
    prop: 'customerName',
    label: '客户',
    align: 'left',
    minWidth: '150px'
  },
  {
    prop: 'terminalCustomerName',
    label: '终端客户/子客户',
    align: 'left',
    minWidth: '160px',
    renderTemplate: (scope) => {
      return (
        <span>
          {scope.row.terminalCustomerName && <span>{scope.row.terminalCustomerName}</span>}
          {scope.row.slaveCustomerName && <span>{scope.row.slaveCustomerName}</span>}
        </span>
      )
    }
  },
  {
    prop: 'orderType',
    label: '类型',
    align: 'left',
    width: '120px',
    renderTemplate: (scope) => {
      return <div>{orderTypeMapper(scope.row.orderType)}</div>
    }
  },
  {
    prop: 'processorName',
    label: '处理人',
    align: 'left',
    width: '150px'
  },
  {
    prop: 'pcsStatus',
    label: '状态',
    align: 'left',
    width: '150px',
    renderTemplate: (scope) => {
      return (
        <span>
          {scope.row.pcsStatus === 0 && <ElTag type="danger">待评审</ElTag>}
          {scope.row.pcsStatus === 1 && <ElTag type="success">完成</ElTag>}
        </span>
      )
    }
  },
  {
    prop: 'createName',
    label: '创建人',
    align: 'left',
    width: '150px'
  },
  {
    prop: 'addCardUserName',
    label: '卡款添加人',
    align: 'left',
    width: '150px'
  },
  {
    prop: 'pushDate',
    label: '推送时间',
    align: 'left',
    width: '220px'
  }
])

/** 查询按钮操作 */
function handleQuery() {
  queryParams.value.pageNo = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  // UmvQuery的@reset事件会自动重置表单并触发查询
  handleQuery()
}

/** 打开详情页面 */
async function handleOpen(row: any) {
  router.push({
    name: 'PcsOrderDetail',
    query: {
      id: row.id
    }
  })
}

/** 查询列表 */
async function getList() {
  try {
    loading.value = true
    let params: any = {
      pageNo: queryParams.value.pageNo,
      pageSize: queryParams.value.pageSize
    }
    const queryParamsList = ['orderCode', 'customerName', 'cardCode', 'pcsStatus', 'taskCode']
    for (const key of queryParamsList) {
      if (queryParams.value[key] !== '') {
        params = Object.assign(params, {
          [key]: queryParams.value[key]
        })
      }
    }

    const res: any = await pcsOrderApi.pcsTaskFindByPage(params)
    list.value = res.list.map((item: any) => {
      return {
        ...item
      }
    })
    total.value = res.total
  } catch (error) {
    console.error('查询列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 初始化数据
getList()

// 页面激活时刷新数据
onActivated(() => {
  if (!loading.value) {
    getList()
  }
})
</script>
