<!-- PLM订单签审-任务列表 -->
<template>
  <ContentWrap>
    <el-form ref="queryRef" class="mb-20px" :model="queryParams" label-width="100px">
      <el-row>
        <el-col :span="24" :md="8" :lg="6" :xl="6">
          <el-form-item label="任务编号" prop="taskCode">
            <SearchWildcardInput
              v-model.trim="queryParams.taskCode"
              placeholder="请输入任务编号"
              clearable
              maxlength="50"
              style="width: 100%; min-width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24" :md="8" :lg="6" :xl="6">
          <el-form-item label="任务状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="请选择"
              filterable
              clearable
              style="width: 100%; min-width: 100%"
            >
              <el-option
                v-for="dict in statusOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24" :md="8" :lg="6" :xl="6">
          <el-form-item label="订单类型" prop="orderType">
            <el-select
              v-model="queryParams.orderType"
              placeholder="请选择"
              filterable
              clearable
              style="width: 100%; min-width: 100%"
            >
              <el-option
                v-for="dict in orderTypeOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24" :md="8" :lg="6" :xl="6">
          <el-form-item label="UMV订单号" prop="umvOrderCode">
            <SearchWildcardInput
              v-model.trim="queryParams.umvOrderCode"
              placeholder="请输入"
              clearable
              maxlength="50"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24" :md="8" :lg="6" :xl="6">
          <el-form-item label="创建人" prop="createName">
            <SearchWildcardInput
              v-model.trim="queryParams.createName"
              placeholder="请输入创建人"
              clearable
              maxlength="50"
              style="width: 100%; min-width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24" :md="8" :lg="6" :xl="6">
          <el-form-item label="客户名称" prop="customerName">
            <!-- <SearchWildcardInput
              v-model.trim="queryParams.customerName"
              placeholder="请输入客户名称"
              clearable
              maxlength="50"
              style="width: 100%; min-width: 100%"
            /> -->
            <CustomerSelect
              width="100%"
              v-model:customer-name="queryParams.customerName"
              isSearchByName
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="24" :md="8" :lg="6" :xl="6">
          <el-form-item label="产品名称" prop="productName">
            <SearchWildcardInput
              v-model.trim="queryParams.productName"
              placeholder="请输入产品名称"
              clearable
              maxlength="50"
              style="width: 100%; min-width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24" :md="8" :lg="6" :xl="6">
          <el-form-item label="卡款编号" prop="cardCode">
            <SearchWildcardInput
              v-model.trim="queryParams.cardCode"
              placeholder="请输入卡款编号"
              clearable
              maxlength="50"
              style="width: 100%; min-width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24" :md="8" :lg="6" :xl="6">
          <el-form-item label="销售单号" prop="saleOrderCode">
            <SearchWildcardInput
              v-model.trim="queryParams.saleOrderCode"
              placeholder="请输入销售单号"
              clearable
              maxlength="50"
              style="width: 100%; min-width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24" :md="8" :lg="6" :xl="6">
          <el-form-item label="处理人" prop="processorName">
            <SearchWildcardInput
              v-model.trim="queryParams.processorName"
              placeholder="请输入处理人"
              clearable
              maxlength="50"
              style="width: 100%; min-width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24" :md="8" :lg="6" :xl="6">
          <el-form-item label="处理节点" prop="targetCode">
            <el-select
              v-model="queryParams.targetCode"
              placeholder="请选择"
              filterable
              clearable
              style="width: 100%; min-width: 100%"
            >
              <el-option
                v-for="dict in currentNodeOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24" :md="8" :lg="6" :xl="6">
          <el-form-item label="操作内容" prop="operateNode">
            <el-select
              v-model="queryParams.operateNode"
              placeholder="请选择"
              filterable
              clearable
              style="width: 100%; min-width: 100%"
            >
              <el-option
                v-for="dict in operateNodeOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24" :md="8" :lg="6" :xl="6">
          <el-form-item label="操作人" prop="operateUser">
            <SearchWildcardInput
              v-model.trim="queryParams.operateUser"
              placeholder="请输入操作人"
              clearable
              maxlength="50"
              style="width: 100%; min-width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24" :md="10" :lg="8" :xl="6">
          <el-form-item label="操作时间" prop="timeRange">
            <el-date-picker
              v-model="queryParams.timeRange"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              :shortcuts="shortcuts"
              style="width: 100%; min-width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24" :md="8" :lg="6" :xl="6">
          <el-form-item label="K3客户编码" prop="k3CustomerCode">
            <SearchWildcardInput
              v-model.trim="queryParams.k3CustomerCode"
              placeholder="请输入K3客户编码"
              clearable
              maxlength="50"
              style="width: 100%; min-width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24" :md="10" :lg="8" :xl="6">
          <el-form-item label="创建时间" prop="createTimeRange">
            <el-date-picker
              v-model="queryParams.createTimeRange"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              :shortcuts="shortcuts"
              style="width: 100%; min-width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24" :md="14" :lg="10" :xl="10">
          <el-form-item label="" label-width="20px">
            <el-checkbox v-model="queryParams.urgentSign" label="是否加急" />
            <el-checkbox v-model="queryParams.searchSelf" label="只看自己" />
            <el-checkbox v-model="queryParams.searchHistory" label="查看历史" />
            <el-button
              style="margin-left: 20px"
              type="primary"
              v-track:click.btn
              @click="handleQuery"
              >{{ t('common.query') }}</el-button
            >
            <el-button type="warning" @click="resetQuery">{{ t('common.reset') }}</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="handle__btn mb-10px">
      <el-dropdown @command="onExportCommand" style="margin-right: 5px">
        <el-button type="success">
          导出<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item title="导出任务清单" :command="onExport">任务清单</el-dropdown-item>
            <el-dropdown-item title="导出销售或客服NG的卡款清单" :command="onExportNgCardTypes"
              >卡款NG清单</el-dropdown-item
            >
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <!-- <el-button type="success" size="default" v-track:click.btn @click="onExport">导出</el-button> -->
      <el-button
        type="info"
        :icon="Plus"
        size="default"
        :disabled="multiple"
        v-track:click.btn
        @click="onBatchClose"
      >
        批量关闭</el-button
      >
    </div>
    <el-table
      v-loading="loading"
      v-horizontal-scroll
      border
      :data="list"
      :row-class-name="tableRowClassName"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="任务编号" align="left" width="240">
        <template #default="scope">
          <el-tag v-if="scope.row.urgentSign" type="danger" class="mr-5px">急</el-tag>
          <el-link v-track:click.btn type="primary" :underline="false">
            <router-link
              :to="`/CardProductManagement/OrderSign/PlmOrder/Task/List/Detail/${scope.row.id}`"
            >
              {{ scope.row.taskCode }}</router-link
            >
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="产品名称" align="left" prop="productName" min-width="280" />
      <el-table-column label="客户名称" align="left" prop="customerName" min-width="280" />
      <el-table-column
        label="终端客户/子客户"
        align="left"
        prop="terminalCustomerName"
        min-width="160"
      >
        <template #default="{ row }">
          <span v-if="row.terminalCustomerName">{{ row.terminalCustomerName }}</span>
          <span v-if="row.slaveCustomerName">{{ row.slaveCustomerName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="下单数量" align="left" prop="quantity" width="100" />
      <el-table-column label="卡款数量" align="left" prop="cardNums" width="100" />
      <el-table-column label="任务状态" align="left" width="100">
        <template #default="scope">
          <template v-for="item in statusOptions" :key="item.value">
            <span
              v-if="scope.row.status === item.value"
              class="badge"
              :style="{ background: item.color }"
              >{{ item.label }}</span
            >
          </template>
        </template>
      </el-table-column>
      <el-table-column label="处理节点" align="left" width="110">
        <template #default="scope">
          <template v-for="item in currentNodeOptions" :key="item.value">
            <span
              v-if="scope.row.targetCode === item.value"
              class="badge"
              :style="{ background: item.color }"
              >{{ item.label }}</span
            >
          </template>
        </template>
      </el-table-column>
      <el-table-column label="处理人" align="left" prop="processorName" width="100" />
      <el-table-column label="创建人/接单人" align="left" prop="createName" width="130" />
      <el-table-column label="订单类型" align="left" width="110">
        <template #default="scope">
          <template v-for="item in orderTypeOptions" :key="item.value">
            <span
              v-if="scope.row.orderType === item.value"
              class="badge"
              :style="{ background: item.color }"
              >{{ item.label }}</span
            >
          </template>
        </template>
      </el-table-column>
      <el-table-column label="UMV订单单号" align="left" prop="umvOrderCode" width="200" />
      <el-table-column label="客服复审" align="left" prop="cscReviewDate" width="200" />
      <el-table-column label="创建人" align="left" prop="createName" width="100" />
      <el-table-column label="创建时间" align="left" prop="createDate" width="200" />
      <el-table-column label="任务备注" align="left" prop="remarkText" min-width="280" />
      <el-table-column label="产品备注" align="left" prop="productRemark" min-width="280" />
    </el-table>

    <Pagination
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
      :total="total"
    />
  </ContentWrap>
</template>

<script setup lang="ts">
defineOptions({
  name: 'PlmOrderTaskList'
})

import { ContentWrap } from '@/components/ContentWrap'

import { Plus } from '@element-plus/icons-vue'
import { useDataMap } from '@/views/CardProductManagement/OrderSign/PlmOrder/hooks/useDataMap' // 数据码表映射
import * as plmTaskApi from '@/api/OrderHandle/plmTask/index'
import { SearchWildcardInput } from '@/components/SearchWildcardInput'
import download from '@/utils/download'
const router = useRouter()
const message = useMessage()

const { t } = useI18n()
//时间段
const shortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  }
]

const queryRef = ref()
const queryParams = ref({
  taskCode: '',
  status: '',
  orderType: '',
  createName: '',
  umvOrderCode: '',
  customerName: '',
  productName: '',
  cardCode: '',
  saleOrderCode: '',
  processorName: '',
  targetCode: '',
  operateNode: '',
  operateUser: '',
  timeRange: [],
  searchSelf: true,
  urgentSign: false,
  searchHistory: false,
  k3CustomerCode: '',
  pageNo: 1,
  pageSize: 10,
  createTimeRange: []
})
const loading = ref(false)
const list = ref([])
const total = ref(0)
const { statusOptions, orderTypeOptions, currentNodeOptions, remarkTypeMapper } = useDataMap()
const operateNodeOptions: any = ref([...currentNodeOptions.value])

// 任务备注格式化
const taskRemarkFormater = (val) => {
  let text = ''
  if (val && val.type && Array.isArray(val.type)) {
    for (const item of val.type) {
      text += `${remarkTypeMapper(item)};`
    }
  }
  if (val && val.msg) {
    text += `${val.msg}`
  }
  text = text.trim()
  return text ? text : ''
}

/** 生成序列号 */
function setIndex(index) {
  const num = index + 1 + (queryParams.value.pageNo - 1) * queryParams.value.pageSize
  return num
}

const tableRowClassName = ({ row, rowIndex }) => {
  if (row.urgentSign) {
    return 'warning-row'
  }
}
const ids = ref([])
const multiple = ref(true)
/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id)
  multiple.value = !selection.length
}

/**导出操作 */
async function onExport() {
  const params = convertQueryParams(queryParams)
  const res = await plmTaskApi.exportList(params)
  const fileName = decodeURI(res['headers']['content-disposition']).split('filename=')[1]
  download.excel(res.data, fileName)
}
/** 导出NG卡款清单 */
async function onExportNgCardTypes() {
  if (!queryParams.value.timeRange || queryParams.value.timeRange.length <= 0) {
    message.error('请选择操作时间')
    return
  }

  if (!queryParams.value.operateNode) {
    message.error('请选择操作内容')
    return
  }
  const params = convertQueryParams(queryParams)
  const res = await plmTaskApi.exportNgCardTypeList(params)
  const fileName = decodeURI(res['headers']['content-disposition']).split('filename=')[1]
  download.excel(res.data, fileName)
}

function convertQueryParams(queryParams: any) {
  let params = {
    pageNo: queryParams.value.pageNo,
    pageSize: queryParams.value.pageSize
  }
  const queryParamsList = [
    'taskCode',
    'urgentSign',
    'umvOrderCode',
    'status',
    'orderType',
    'createName',
    'customerName',
    'productName',
    'cardCode',
    'saleOrderCode',
    'processorName',
    'targetCode',
    'operateNode',
    'operateUser',
    'searchSelf',
    'searchHistory',
    'k3CustomerCode'
  ]
  for (const key of queryParamsList) {
    if (queryParams.value[key] !== '') {
      params = Object.assign(params, {
        [key]: queryParams.value[key]
      })
    }
  }
  if (queryParams.value.timeRange && queryParams.value.timeRange.length > 0) {
    params = Object.assign(params, {
      updateDateBegin: `${queryParams.value.timeRange[0]} 00:00:00`,
      updateDateEnd: `${queryParams.value.timeRange[1]} 23:59:59`
    })
  }

  if (queryParams.value.createTimeRange && queryParams.value.createTimeRange.length > 0) {
    params = Object.assign(params, {
      createDateBegin: `${queryParams.value.createTimeRange[0]} 00:00:00`,
      createDateEnd: `${queryParams.value.createTimeRange[1]} 23:59:59`
    })
  }

  return params
}

/** 批量关闭 */
async function onBatchClose() {
  try {
    console.log(ids.value)
    if (ids.value.length == 0) {
      message.warning('请选择任务')
      return
    }

    await message.confirm('确认关闭订单任务？', '提示')
    loading.value = true
    const params = {
      taskIds: ids.value
    }
    const res = await plmTaskApi.reviewTaskClose(params)
    console.log(res)
  } finally {
    loading.value = false
  }
}

/** 查询按钮操作 */
function handleQuery() {
  queryParams.value.pageNo = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  queryRef.value.resetFields()
  queryParams.value.searchSelf = true
  queryParams.value.searchHistory = false
  handleQuery()
}

/** 查询列表 */
async function getList() {
  try {
    loading.value = true
    const params = convertQueryParams(queryParams)
    const res: any = await plmTaskApi.plmTaskFindByPage(params)
    list.value = res.list.map((item) => {
      return {
        ...item,
        remarkText: taskRemarkFormater(item.remark)
      }
    })
    total.value = res.total
  } finally {
    loading.value = false
  }
}

async function handleOpen(row) {
  router.push({
    path: `/CardProductManagement/OrderSign/PlmOrder/Task/List/Detail/${row.id}`
  })
}

async function onExportCommand(callback) {
  if (callback) {
    callback()
  }
}

getList()
onActivated(() => {
  if (!loading.value) {
    getList()
  }
})
</script>

<style lang="less" scoped>
.badge {
  display: inline-block;
  min-width: 10px;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 700;
  color: #fff;
  line-height: 1;
  vertical-align: baseline;
  white-space: nowrap;
  text-align: center;
  background-color: #999;
  border-radius: 10px;
}

.handle__btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.el-table {
  :deep(.warning-row) {
    --el-table-tr-bg-color: var(--el-color-warning-light-9);
  }
}
</style>
