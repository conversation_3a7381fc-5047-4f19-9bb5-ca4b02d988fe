<!-- PLM订单签审-订单列表 -->
<template>
  <UmvContent>
    <UmvQuery
      v-model="queryParams"
      :opts="queryOpts"
      label-width="110px"
      isExpansion
      @check="handleQuery"
      @reset="resetQuery"
      :col-length-map="{
        2400: 6, // 屏幕宽度 >= 2560px 时使用 8 列
        1900: 5, // 屏幕宽度 >= 1920px 时使用 8 列
        1600: 4, // 屏幕宽度 >= 1600px 且 < 1920px 时使用 6 列
        1280: 4, // 屏幕宽度 >= 1280px 且 < 1600px 时使用 5 列
        1100: 3, // 屏幕宽度 >= 1280px 且 < 1600px 时使用 5 列
        1000: 2, // 屏幕宽度 >= 1000px 且 < 1280px 时使用 3 列
        768: 2, // 屏幕宽度 >= 768px 且 < 1000px 时使用 2 列
        0: 1 // 屏幕宽度 < 768px 时使用 1 列
      }"
    />

    <UmvTable v-loading="loading" :data="list" :columns="columns" @refresh="getList">
      <template #tools>
        <el-button type="success" v-track:click.btn @click="onExport">导出</el-button>
      </template>
    </UmvTable>

    <Pagination
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
      :total="total"
    />
  </UmvContent>
</template>

<script setup lang="tsx">
import { ref, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import UmvContent from '@/components/UmvContent'
import { UmvQuery, type QueryOption, type QueryForm } from '@/components/UmvQuery'
import UmvTable from '@/components/UmvTable'
import type { TableColumn } from '@/components/UmvTable/src/types'
import { SearchWildcardInput } from '@/components/SearchWildcardInput'
import { CustomerSelect } from '@/components/CustomerSelect'
import Pagination from '@/components/Pagination/index.vue'
import { useDataMap } from '@/views/CardProductManagement/OrderSign/PlmOrder/hooks/useDataMap'
import * as plmOrderApi from '@/api/OrderHandle/plmOrder/index'
import download from '@/utils/download'
import { ElInput, ElSelect, ElOption, ElCheckbox, ElDatePicker, ElLink } from 'element-plus'

defineOptions({
  name: 'PlmOrderList'
})

const { t } = useI18n()
const router = useRouter()
const message = useMessage()
const { orderTypeOptions: typeOptions, orderStatusOptions } = useDataMap()

// 时间段快捷选项
const shortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  }
]

// 查询条件数据
const queryParams = ref({
  orderCode: '',
  status: '',
  type: '',
  createName: '',
  productName: '',
  k3CustomerName: '',
  terminalCustomerName: '',
  umvOrderCode: '',
  timeRange: [],
  searchSelf: true,
  searchHistory: false,
  pageNo: 1,
  pageSize: 10
})

// 查询配置
const queryOpts = ref<Record<string, QueryOption>>({
  orderCode: {
    label: '任务编号',
    defaultVal: '',
    controlRender: (form: any) => (
      <SearchWildcardInput
        v-model={form.orderCode}
        placeholder="请输入"
        clearable
        maxlength="50"
        style="width: 100%"
      />
    )
  },
  umvOrderCode: {
    label: 'UMV订单号',
    defaultVal: '',
    controlRender: (form: any) => (
      <SearchWildcardInput
        v-model={form.umvOrderCode}
        placeholder="请输入"
        clearable
        maxlength="50"
        style="width: 100%"
      />
    )
  },
  status: {
    label: '订单状态',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElSelect
        v-model={form.status}
        placeholder="请选择"
        filterable
        clearable
        style="width: 100%; min-width: 100%"
      >
        {orderStatusOptions.value.map((dict) => (
          <ElOption key={dict.value} label={dict.label} value={dict.value} />
        ))}
      </ElSelect>
    )
  },
  type: {
    label: '订单类型',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElSelect
        v-model={form.type}
        placeholder="请选择"
        filterable
        clearable
        style="width: 100%; min-width: 100%"
      >
        {typeOptions.value.map((dict) => (
          <ElOption key={dict.value} label={dict.label} value={dict.value} />
        ))}
      </ElSelect>
    )
  },
  createName: {
    label: '创建人/接单人',
    defaultVal: '',
    controlRender: (form: any) => (
      <SearchWildcardInput
        v-model={form.createName}
        placeholder="请输入创建人/接单人"
        clearable
        maxlength="50"
        style="width: 100%"
      />
    )
  },
  productName: {
    label: '产品名称',
    defaultVal: '',
    controlRender: (form: any) => (
      <SearchWildcardInput
        v-model={form.productName}
        placeholder="请输入产品名称"
        clearable
        maxlength="50"
        style="width: 100%"
      />
    )
  },
  k3CustomerName: {
    label: '客户名称',
    defaultVal: '',
    controlRender: (form: any) => (
      <CustomerSelect
        v-model:customer-name={form.k3CustomerName}
        isSearchByName
        placeholder="请输入客户名称"
        style="width: 100%"
      />
    )
  },
  terminalCustomerName: {
    label: '终端客户',
    defaultVal: '',
    controlRender: (form: any) => (
      <SearchWildcardInput
        v-model={form.terminalCustomerName}
        placeholder="请输入终端客户"
        clearable
        maxlength="50"
        style="width: 100%"
      />
    )
  },
  timeRange: {
    label: '创建时间',
    defaultVal: [],
    controlRender: (form: any) => (
      <ElDatePicker
        v-model={form.timeRange}
        type="daterange"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="YYYY-MM-DD"
        shortcuts={shortcuts}
        style="width: 100%"
      />
    )
  },
  searchOptions: {
    label: '',
    hideLabel: true,
    defaultVal: '',
    controlRender: (form: any) => (
      <div class="flex items-center space-x-4">
        <ElCheckbox v-model={form.searchSelf} label="只看自己" onChange={getList} />
        <ElCheckbox v-model={form.searchHistory} label="查看历史" onChange={getList} />
      </div>
    )
  }
})

// 表格数据
const loading = ref(false)
const list = ref([])
const total = ref(0)

// 表格列配置
const columns = ref<TableColumn[]>([
  {
    prop: 'index',
    label: '序号',
    width: '100px',
    align: 'center',
    renderTemplate: (scope) => <span>{setIndex(scope.$index)}</span>
  },
  {
    prop: 'orderCode',
    label: '任务编号',
    width: '250px',
    align: 'left',
    renderTemplate: (scope) => (
      <ElLink type="primary" underline={false} onClick={() => handleOpen(scope.row)}>
        {scope.row.orderCode}
      </ElLink>
    )
  },
  {
    prop: 'umvOrderCode',
    label: 'UMV订单编号',
    width: '200px',
    align: 'left',
    renderTemplate: (scope) => (
      <div>
        {scope.row.umvOrderCode}
        {validateOcrOrder(scope.row.channel) && <span class="badge badge-ocr ml-2">OCR</span>}
      </div>
    )
  },
  {
    prop: 'k3CustomerName',
    label: '客户名称',
    minWidth: '150px',
    align: 'left'
  },
  {
    prop: 'terminalCustomerName',
    label: '终端客户/子客户',
    minWidth: '160px',
    align: 'left',
    renderTemplate: (scope) => (
      <div>
        {scope.row.terminalCustomerName && <span>{scope.row.terminalCustomerName}</span>}
        {scope.row.slaveCustomerName && <span>{scope.row.slaveCustomerName}</span>}
      </div>
    )
  },
  {
    prop: 'productCount',
    label: '产品数量',
    width: '100px',
    align: 'center'
  },
  {
    prop: 'complianceResultName',
    label: '订单合规性评审',
    width: '140px',
    align: 'center',
    renderTemplate: (scope) => (
      <span
        class="badge"
        style={{
          background:
            scope.row.complianceResultName === '通过' ? 'rgb(169, 216, 110)' : 'rgb(153, 153, 153)'
        }}
      >
        {scope.row.complianceResultName}
      </span>
    )
  },
  {
    prop: 'status',
    label: '订单状态',
    width: '100px',
    align: 'center',
    renderTemplate: (scope) => {
      const statusItem = orderStatusOptions.value.find((item) => item.value === scope.row.status)
      return statusItem ? (
        <span class="badge" style={{ background: statusItem.color }}>
          {statusItem.label}
        </span>
      ) : null
    }
  },
  {
    prop: 'type',
    label: '订单类型',
    width: '110px',
    align: 'center',
    renderTemplate: (scope) => {
      const typeItem = typeOptions.value.find((item) => item.value === scope.row.type)
      return typeItem ? (
        <span class="badge" style={{ background: typeItem.color }}>
          {typeItem.label}
        </span>
      ) : null
    }
  },
  {
    prop: 'billName',
    label: '下单人',
    width: '120px',
    align: 'center'
  },
  {
    prop: 'createName',
    label: '创建人/接单人',
    width: '120px',
    align: 'center'
  },
  {
    prop: 'createDate',
    label: '创建时间',
    width: '170px',
    align: 'center'
  }
])

/** 生成序列号 */
function setIndex(index: number) {
  const num = index + 1 + (queryParams.value.pageNo - 1) * queryParams.value.pageSize
  return num
}

/** 查询按钮操作 */
function handleQuery() {
  queryParams.value.pageNo = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.value.searchSelf = true
  queryParams.value.searchHistory = false
  handleQuery()
}

/**导出操作 */
async function onExport() {
  try {
    const params = convertQueryParams(queryParams)
    const res = await plmOrderApi.exportList(params)
    const fileName = decodeURI(res['headers']['content-disposition']).split('filename=')[1]
    download.excel(res.data, fileName)
  } catch (error) {
    console.error('导出失败:', error)
  }
}

function convertQueryParams(queryParams: any) {
  let params = {
    pageNo: queryParams.value.pageNo,
    pageSize: queryParams.value.pageSize
  }
  const queryParamsList = [
    'orderCode',
    'status',
    'type',
    'createName',
    'productName',
    'k3CustomerName',
    'terminalCustomerName',
    'searchHistory',
    'searchSelf',
    'umvOrderCode'
  ]
  for (const key of queryParamsList) {
    if (queryParams.value[key] !== '') {
      params = Object.assign(params, {
        [key]: queryParams.value[key]
      })
    }
  }
  if (queryParams.value.timeRange && queryParams.value.timeRange.length > 0) {
    params = Object.assign(params, {
      createDateBegin: `${queryParams.value.timeRange[0]} 00:00:00`,
      createDateEnd: `${queryParams.value.timeRange[1]} 23:59:59`
    })
  }

  return params
}

/** 查询列表 */
async function getList() {
  try {
    loading.value = true
    const params = convertQueryParams(queryParams)
    const res: any = await plmOrderApi.plmOrderFindByPage(params)
    list.value = res.list.map((item: any) => {
      return {
        ...item,
        productCount: item.productNum ? item.productNum : 0
      }
    })
    total.value = res.total
  } catch (error) {
    console.error('查询列表失败:', error)
  } finally {
    loading.value = false
  }
}

async function handleOpen(row: any) {
  //判断订单状态是否为 客服确认
  //判断订单是否OCR订单，确认进去的订单来源
  if (row.status == 'RECEIVE_CONFIRM' && validateOcrOrder(row.channel)) {
    //客服确认 & OCR下单
    router.push({
      name: 'ocrCheckView',
      query: {
        id: row.id
      }
    })
  } else {
    router.push({
      path: '/CardProductManagement/OrderSign/PlmOrder/Order/List/Detail',
      query: {
        id: row.id
      }
    })
  }
}

//判断订单渠道来源，显示OCR标识
function validateOcrOrder(orderChannel: string) {
  //判断是否为邮件OCR或SAAS_OCR下单
  return orderChannel == 'MAIL_OCR' || orderChannel == 'SAAS_OCR'
}

// 初始化数据
getList()

// 页面激活时刷新数据
onActivated(() => {
  if (!loading.value) {
    getList()
  }
})
</script>

<style lang="less">
.badge {
  display: inline-block;
  min-width: 10px;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 700;
  color: #fff;
  line-height: 1;
  vertical-align: baseline;
  white-space: nowrap;
  text-align: center;
  background-color: #999;
  border-radius: 10px;
}

.badge-ocr {
  background: rgb(64, 158, 255);
  margin-left: 6px;
  padding: 4px;
  font-size: 10px;
  font-weight: 300;
  line-height: 0.6em;
}
</style>
